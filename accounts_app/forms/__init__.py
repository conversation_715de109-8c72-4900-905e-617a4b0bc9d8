"""
Forms package for accounts_app.

This package contains all form classes organized by feature area for better maintainability.
All forms are imported here to maintain backward compatibility.
"""

# --- Local App Imports ---
from .common import AccessibleFormMixin, AccountDeactivationForm
from .customer import (
    CustomerLoginForm,
    CustomerPasswordChangeForm,
    CustomerProfileForm,
    CustomerSignupForm,
)
from .provider import (
    ServiceProviderLoginForm,
    ServiceProviderPasswordChangeForm,
    ServiceProviderProfileForm,
    ServiceProviderSignupForm,
)
from .team import TeamMemberForm

# Make all forms available at package level for backward compatibility
__all__ = [
    # Common forms and mixins
    "AccessibleFormMixin",
    "AccountDeactivationForm",
    # Customer forms
    "CustomerSignupForm",
    "CustomerLoginForm",
    "CustomerProfileForm",
    "CustomerPasswordChangeForm",
    # Service provider forms
    "ServiceProviderSignupForm",
    "ServiceProviderLoginForm",
    "ServiceProviderProfileForm",
    "ServiceProviderPasswordChangeForm",
    # Team management forms
    "TeamMemberForm",
]
