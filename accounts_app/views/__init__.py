# --- Import all views for compatibility ---

# Import email utilities for test patching compatibility
from utility_app.email_utils import send_welcome_email

# Common utilities and business landing page
from .common import MESSAGES, business_landing_view, get_client_ip, record_login_attempt

# Customer views
from .customer import (
    CustomerPasswordResetCompleteView,
    CustomerPasswordResetConfirmView,
    CustomerPasswordResetDoneView,
    CustomerPasswordResetView,
    CustomerProfileEditView,
    CustomerProfileView,
    CustomerSignupView,
    customer_change_password_view,
    customer_deactivate_account_view,
    customer_login_view,
    unified_logout_view,
)

# Provider views
from .provider import (
    ServiceProviderPasswordResetCompleteView,
    ServiceProviderPasswordResetConfirmView,
    ServiceProviderPasswordResetDoneView,
    ServiceProviderPasswordResetView,
    ServiceProviderProfileEditView,
    ServiceProviderProfileView,
    ServiceProviderSignupView,
    premium_upgrade,
    provider_email_verify_view,
    provider_signup_done_view,
    service_provider_change_password_view,
    service_provider_deactivate_account_view,
    service_provider_login_view,
)

# Team management views
from .team import (
    team_member_add_view,
    team_member_delete_view,
    team_member_edit_view,
    team_member_list_view,
    team_member_toggle_status_view,
)

# Make all views available at package level for backward compatibility
__all__ = [
    # Email utilities
    "send_welcome_email",
    # Common
    "get_client_ip",
    "record_login_attempt",
    "business_landing_view",
    "MESSAGES",
    # Customer views
    "CustomerSignupView",
    "customer_login_view",
    "unified_logout_view",
    "CustomerProfileView",
    "CustomerProfileEditView",
    "customer_change_password_view",
    "customer_deactivate_account_view",
    "CustomerPasswordResetView",
    "CustomerPasswordResetDoneView",
    "CustomerPasswordResetConfirmView",
    "CustomerPasswordResetCompleteView",
    # Provider views
    "ServiceProviderSignupView",
    "provider_signup_done_view",
    "provider_email_verify_view",
    "service_provider_login_view",
    "ServiceProviderProfileView",
    "ServiceProviderProfileEditView",
    "service_provider_change_password_view",
    "service_provider_deactivate_account_view",
    "ServiceProviderPasswordResetView",
    "ServiceProviderPasswordResetDoneView",
    "ServiceProviderPasswordResetConfirmView",
    "ServiceProviderPasswordResetCompleteView",
    # Team management views
    "team_member_list_view",
    "team_member_add_view",
    "team_member_edit_view",
    "team_member_delete_view",
    "team_member_toggle_status_view",
    # Premium features
    "premium_upgrade",
]
